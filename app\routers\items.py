from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from app.auth.dependencies import get_current_user, require_roles
from app.database.connection import get_db
from app.crud import item as item_crud, user as user_crud
from app.schemas.item import ItemCreate, ItemUpdate, ItemResponse, CategoryCreate, CategoryUpdate, CategoryResponse
from typing import Dict, Any, List, Optional

router = APIRouter(prefix="/items", tags=["items"])


@router.get("/", response_model=List[ItemResponse])
async def get_items(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    my_items: bool = Query(False, description="Filter to show only current user's items"),
    db: Session = Depends(get_db),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Get list of items with optional filtering
    """
    owner_id = None
    if my_items:
        # Get current user from database
        keycloak_id = current_user.get("sub")
        user = user_crud.get_user_by_keycloak_id(db, keycloak_id)
        if user:
            owner_id = user.id
    
    return item_crud.get_items(db, skip=skip, limit=limit, owner_id=owner_id)


@router.post("/", response_model=ItemResponse)
async def create_item(
    item: ItemCreate,
    db: Session = Depends(get_db),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Create new item
    """
    # Get current user from database
    keycloak_id = current_user.get("sub")
    user = user_crud.get_user_by_keycloak_id(db, keycloak_id)
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found in database. Please sync your user first."
        )
    
    return item_crud.create_item(db, item, user.id)


@router.get("/{item_id}", response_model=ItemResponse)
async def get_item(
    item_id: int,
    db: Session = Depends(get_db),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Get item by ID
    """
    item = item_crud.get_item_by_id(db, item_id)
    if not item:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Item not found"
        )
    return item


@router.put("/{item_id}", response_model=ItemResponse)
async def update_item(
    item_id: int,
    item_update: ItemUpdate,
    db: Session = Depends(get_db),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Update item (only owner can update)
    """
    # Get current user from database
    keycloak_id = current_user.get("sub")
    user = user_crud.get_user_by_keycloak_id(db, keycloak_id)
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found in database"
        )
    
    updated_item = item_crud.update_item(db, item_id, item_update, user.id)
    if not updated_item:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Item not found or you don't have permission to update it"
        )
    
    return updated_item


@router.delete("/{item_id}")
async def delete_item(
    item_id: int,
    db: Session = Depends(get_db),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Delete item (only owner can delete)
    """
    # Get current user from database
    keycloak_id = current_user.get("sub")
    user = user_crud.get_user_by_keycloak_id(db, keycloak_id)
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found in database"
        )
    
    success = item_crud.delete_item(db, item_id, user.id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Item not found or you don't have permission to delete it"
        )
    
    return {"message": "Item deleted successfully"}


# Category endpoints
@router.get("/categories/", response_model=List[CategoryResponse])
async def get_categories(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: Session = Depends(get_db),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Get list of categories
    """
    return item_crud.get_categories(db, skip=skip, limit=limit)


@router.post("/categories/", response_model=CategoryResponse)
async def create_category(
    category: CategoryCreate,
    db: Session = Depends(get_db),
    current_user: Dict[str, Any] = Depends(require_roles(["admin", "moderator"]))
):
    """
    Create new category (admin/moderator only)
    """
    return item_crud.create_category(db, category)


@router.put("/categories/{category_id}", response_model=CategoryResponse)
async def update_category(
    category_id: int,
    category_update: CategoryUpdate,
    db: Session = Depends(get_db),
    current_user: Dict[str, Any] = Depends(require_roles(["admin", "moderator"]))
):
    """
    Update category (admin/moderator only)
    """
    updated_category = item_crud.update_category(db, category_id, category_update)
    if not updated_category:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Category not found"
        )
    
    return updated_category


@router.delete("/categories/{category_id}")
async def delete_category(
    category_id: int,
    db: Session = Depends(get_db),
    current_user: Dict[str, Any] = Depends(require_roles(["admin"]))
):
    """
    Delete category (admin only)
    """
    success = item_crud.delete_category(db, category_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Category not found"
        )
    
    return {"message": "Category deleted successfully"}
