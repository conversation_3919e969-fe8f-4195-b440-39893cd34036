from sqlalchemy.orm import Session
from app.models.item import Item, Category
from app.schemas.item import ItemCreate, ItemUpdate, CategoryCreate, CategoryUpdate
from typing import Optional, List


def get_item_by_id(db: Session, item_id: int) -> Optional[Item]:
    """Get item by ID"""
    return db.query(Item).filter(Item.id == item_id).first()


def get_items(db: Session, skip: int = 0, limit: int = 100, owner_id: Optional[int] = None) -> List[Item]:
    """Get list of items with pagination and optional owner filter"""
    query = db.query(Item)
    if owner_id:
        query = query.filter(Item.owner_id == owner_id)
    return query.offset(skip).limit(limit).all()


def create_item(db: Session, item: ItemCreate, owner_id: int) -> Item:
    """Create new item"""
    db_item = Item(
        title=item.title,
        description=item.description,
        owner_id=owner_id
    )
    db.add(db_item)
    db.commit()
    db.refresh(db_item)
    return db_item


def update_item(db: Session, item_id: int, item_update: ItemUpdate, owner_id: int) -> Optional[Item]:
    """Update item (only if user is owner)"""
    db_item = db.query(Item).filter(Item.id == item_id, Item.owner_id == owner_id).first()
    if not db_item:
        return None
    
    update_data = item_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_item, field, value)
    
    db.commit()
    db.refresh(db_item)
    return db_item


def delete_item(db: Session, item_id: int, owner_id: int) -> bool:
    """Delete item (only if user is owner)"""
    db_item = db.query(Item).filter(Item.id == item_id, Item.owner_id == owner_id).first()
    if not db_item:
        return False
    
    db.delete(db_item)
    db.commit()
    return True


# Category CRUD operations
def get_category_by_id(db: Session, category_id: int) -> Optional[Category]:
    """Get category by ID"""
    return db.query(Category).filter(Category.id == category_id).first()


def get_categories(db: Session, skip: int = 0, limit: int = 100) -> List[Category]:
    """Get list of categories with pagination"""
    return db.query(Category).offset(skip).limit(limit).all()


def create_category(db: Session, category: CategoryCreate) -> Category:
    """Create new category"""
    db_category = Category(
        name=category.name,
        description=category.description
    )
    db.add(db_category)
    db.commit()
    db.refresh(db_category)
    return db_category


def update_category(db: Session, category_id: int, category_update: CategoryUpdate) -> Optional[Category]:
    """Update category"""
    db_category = get_category_by_id(db, category_id)
    if not db_category:
        return None
    
    update_data = category_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_category, field, value)
    
    db.commit()
    db.refresh(db_category)
    return db_category


def delete_category(db: Session, category_id: int) -> bool:
    """Delete category"""
    db_category = get_category_by_id(db, category_id)
    if not db_category:
        return False
    
    db.delete(db_category)
    db.commit()
    return True
