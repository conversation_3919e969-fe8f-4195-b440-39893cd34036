#!/usr/bin/env python3
"""
Setup script for FastAPI Keycloak PostgreSQL application
"""

import os
import sys
import subprocess
from pathlib import Path

def run_command(command, description):
    """Run a shell command and handle errors"""
    print(f"Running: {description}")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✓ {description} completed successfully")
        return result.stdout
    except subprocess.CalledProcessError as e:
        print(f"✗ {description} failed: {e}")
        print(f"Error output: {e.stderr}")
        return None

def check_requirements():
    """Check if required tools are installed"""
    print("Checking requirements...")
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("✗ Python 3.8 or higher is required")
        return False
    print(f"✓ Python {sys.version_info.major}.{sys.version_info.minor} detected")
    
    # Check if pip is available
    if run_command("pip --version", "Checking pip") is None:
        return False
    
    return True

def install_dependencies():
    """Install Python dependencies"""
    print("\nInstalling dependencies...")
    return run_command("pip install -r requirements.txt", "Installing Python packages")

def setup_environment():
    """Setup environment file"""
    print("\nSetting up environment...")
    
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists() and env_example.exists():
        run_command("cp .env.example .env", "Creating .env file from template")
        print("⚠️  Please edit .env file with your actual configuration values")
    elif env_file.exists():
        print("✓ .env file already exists")
    else:
        print("✗ No .env.example file found")

def initialize_database():
    """Initialize database with Alembic"""
    print("\nInitializing database...")
    
    # Initialize Alembic if not already done
    if not Path("alembic/versions").exists():
        run_command("alembic init alembic", "Initializing Alembic")
    
    # Create initial migration
    run_command("alembic revision --autogenerate -m 'Initial migration'", "Creating initial migration")
    
    # Apply migrations
    run_command("alembic upgrade head", "Applying database migrations")

def main():
    """Main setup function"""
    print("FastAPI Keycloak PostgreSQL Setup")
    print("=" * 40)
    
    if not check_requirements():
        print("\n✗ Setup failed: Requirements not met")
        sys.exit(1)
    
    if install_dependencies() is None:
        print("\n✗ Setup failed: Could not install dependencies")
        sys.exit(1)
    
    setup_environment()
    
    print("\n" + "=" * 40)
    print("Setup completed!")
    print("\nNext steps:")
    print("1. Edit .env file with your Keycloak and PostgreSQL configuration")
    print("2. Run database migrations: alembic upgrade head")
    print("3. Start the application: uvicorn app.main:app --reload")
    print("4. Visit http://localhost:8000/docs for API documentation")

if __name__ == "__main__":
    main()
