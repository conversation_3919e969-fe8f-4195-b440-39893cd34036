# FastAPI Keycloak PostgreSQL Integration

A complete FastAPI application with Keycloak authentication and PostgreSQL database integration, designed for cloud deployment.

## Features

- 🔐 **Keycloak Authentication**: JWT token validation and user management
- 🗄️ **PostgreSQL Integration**: SQLAlchemy ORM with Alembic migrations
- 🚀 **FastAPI Framework**: Modern, fast web framework with automatic API documentation
- 🔒 **Role-based Access Control**: Protect endpoints with user roles and scopes
- 📊 **Database Models**: User management and example item/category models
- 🐳 **Docker Support**: Ready for containerized deployment
- 📝 **API Documentation**: Automatic Swagger/OpenAPI documentation

## Prerequisites

- Python 3.8+
- PostgreSQL database (cloud or local)
- Keycloak server (cloud or local)
- Docker (optional)

## Quick Start

### 1. Clone and Setup

```bash
git clone <your-repo>
cd FastApi
python scripts/setup.py
```

### 2. Configure Environment

Copy `.env.example` to `.env` and update with your cloud server configurations:

```env
# Database Configuration
DATABASE_URL=*****************************************************/your_database

# Keycloak Configuration
KEYCLOAK_SERVER_URL=https://your-keycloak-server.com/auth/
KEYCLOAK_REALM=your-realm
KEYCLOAK_CLIENT_ID=your-client-id
KEYCLOAK_CLIENT_SECRET=your-client-secret

# Application Configuration
SECRET_KEY=your-secret-key-here
```

### 3. Install Dependencies

```bash
pip install -r requirements.txt
```

### 4. Initialize Database

```bash
# Create migration
alembic revision --autogenerate -m "Initial migration"

# Apply migration
alembic upgrade head
```

### 5. Run Application

```bash
# Development
uvicorn app.main:app --reload

# Production
uvicorn app.main:app --host 0.0.0.0 --port 8000
```

## API Endpoints

### Authentication
- `GET /auth/me` - Get current user info from token
- `GET /auth/userinfo` - Get user info from Keycloak
- `POST /auth/sync-user` - Sync user to local database
- `GET /auth/validate-token` - Validate JWT token

### Users
- `GET /users/` - List users (admin only)
- `GET /users/me` - Get current user profile
- `PUT /users/me` - Update current user profile
- `GET /users/{user_id}` - Get user by ID (admin only)
- `GET /users/me/profile` - Get detailed user profile
- `POST /users/me/profile` - Create detailed user profile
- `PUT /users/me/profile` - Update detailed user profile

### Items
- `GET /items/` - List items (with filtering)
- `POST /items/` - Create new item
- `GET /items/{item_id}` - Get item by ID
- `PUT /items/{item_id}` - Update item (owner only)
- `DELETE /items/{item_id}` - Delete item (owner only)

### Categories
- `GET /items/categories/` - List categories
- `POST /items/categories/` - Create category (admin/moderator)
- `PUT /items/categories/{id}` - Update category (admin/moderator)
- `DELETE /items/categories/{id}` - Delete category (admin only)

## Authentication Flow

1. **Get Token from Keycloak**: Use your Keycloak client to obtain a JWT token
2. **Include Token in Requests**: Add `Authorization: Bearer <token>` header
3. **Automatic Validation**: The app validates tokens with your Keycloak server
4. **Role-based Access**: Endpoints check user roles and scopes automatically

### Example Token Request (using curl)

```bash
# Get token from Keycloak
curl -X POST "https://your-keycloak-server.com/auth/realms/your-realm/protocol/openid-connect/token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "grant_type=password" \
  -d "client_id=your-client-id" \
  -d "client_secret=your-client-secret" \
  -d "username=your-username" \
  -d "password=your-password"

# Use token in API requests
curl -X GET "http://localhost:8000/auth/me" \
  -H "Authorization: Bearer <your-jwt-token>"
```

## Docker Deployment

```bash
# Build and run with Docker Compose
docker-compose up --build

# Or build and run manually
docker build -t fastapi-keycloak .
docker run -p 8000:8000 --env-file .env fastapi-keycloak
```

## Database Models

### User
- Basic user information synced from Keycloak
- Keycloak ID mapping for authentication
- Extended profile information

### Item
- Example business entity with CRUD operations
- Owner-based access control
- Timestamps and soft delete support

### Category
- Organizational structure for items
- Admin-controlled management

## Security Features

- **JWT Token Validation**: Tokens verified against Keycloak public key
- **Role-based Access Control**: Endpoints protected by user roles
- **Scope Validation**: Fine-grained permissions using OAuth scopes
- **Owner-based Access**: Users can only modify their own resources
- **Token Introspection**: Active token validation with Keycloak

## API Documentation

Once running, visit:
- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **Health Check**: http://localhost:8000/health

## Development

### Project Structure

```
app/
├── auth/              # Authentication logic
├── crud/              # Database operations
├── database/          # Database connection and setup
├── models/            # SQLAlchemy models
├── routers/           # API route handlers
├── schemas/           # Pydantic schemas
├── config.py          # Configuration management
└── main.py           # FastAPI application

alembic/              # Database migrations
scripts/              # Setup and utility scripts
```

### Adding New Endpoints

1. Create Pydantic schemas in `app/schemas/`
2. Define database models in `app/models/`
3. Implement CRUD operations in `app/crud/`
4. Create API routes in `app/routers/`
5. Include router in `app/main.py`

### Database Migrations

```bash
# Create new migration
alembic revision --autogenerate -m "Description of changes"

# Apply migrations
alembic upgrade head

# Rollback migration
alembic downgrade -1
```

## Production Considerations

- Use environment variables for all sensitive configuration
- Configure CORS properly for your frontend domains
- Set up proper logging and monitoring
- Use connection pooling for database connections
- Implement rate limiting and request validation
- Set up SSL/TLS termination
- Configure Keycloak with proper security settings

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Check DATABASE_URL format
   - Verify PostgreSQL server is accessible
   - Check firewall and network settings

2. **Keycloak Authentication Failed**
   - Verify KEYCLOAK_SERVER_URL is correct
   - Check client ID and secret
   - Ensure realm name is correct

3. **Token Validation Failed**
   - Check token expiration
   - Verify Keycloak public key access
   - Ensure client configuration in Keycloak

## License

MIT License - see LICENSE file for details.
