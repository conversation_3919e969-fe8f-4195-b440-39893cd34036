from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from app.auth.keycloak_client import keycloak_client
from app.config import settings
from typing import Optional, Dict, Any
import logging

logger = logging.getLogger(__name__)

# Security scheme
security = HTTPBearer(auto_error=False)


async def get_current_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
) -> Dict[str, Any]:
    """
    Dependency to get current authenticated user from JWT token
    """
    # Development mode bypass
    if settings.dev_mode:
        logger.warning("Running in development mode - bypassing authentication")
        return {
            "sub": "dev-user-123",
            "preferred_username": "dev_user",
            "email": "<EMAIL>",
            "given_name": "Dev",
            "family_name": "User",
            "realm_access": {"roles": ["admin", "user"]},
            "scope": "openid profile email"
        }

    if not credentials:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication credentials required",
            headers={"WWW-Authenticate": "Bearer"},
        )

    token = credentials.credentials

    # Verify token with Keycloak
    try:
        token_info = keycloak_client.verify_token(token)
        if not token_info:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication credentials",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # Additional check: introspect token to ensure it's active
        introspection = keycloak_client.introspect_token(token)
        if not introspection or not introspection.get("active", False):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token is not active",
                headers={"WWW-Authenticate": "Bearer"},
            )

        return token_info
    except Exception as e:
        logger.error(f"Authentication error: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication failed",
            headers={"WWW-Authenticate": "Bearer"},
        )


async def get_current_user_info(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
) -> Dict[str, Any]:
    """
    Dependency to get current user info from Keycloak userinfo endpoint
    """
    # Development mode bypass
    if settings.dev_mode:
        return {
            "sub": "dev-user-123",
            "preferred_username": "dev_user",
            "email": "<EMAIL>",
            "given_name": "Dev",
            "family_name": "User",
            "email_verified": True
        }

    if not credentials:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication credentials required",
            headers={"WWW-Authenticate": "Bearer"},
        )

    token = credentials.credentials

    # Get user info from Keycloak
    try:
        user_info = keycloak_client.get_user_info(token)
        if not user_info:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Could not retrieve user information",
                headers={"WWW-Authenticate": "Bearer"},
            )

        return user_info
    except Exception as e:
        logger.error(f"Failed to get user info: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not retrieve user information",
            headers={"WWW-Authenticate": "Bearer"},
        )


def require_roles(required_roles: list[str]):
    """
    Dependency factory to require specific roles
    """
    async def check_roles(
        current_user: Dict[str, Any] = Depends(get_current_user)
    ) -> Dict[str, Any]:
        user_roles = current_user.get("realm_access", {}).get("roles", [])
        
        # Check if user has any of the required roles
        if not any(role in user_roles for role in required_roles):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Insufficient permissions. Required roles: {required_roles}"
            )
        
        return current_user
    
    return check_roles


def require_scope(required_scopes: list[str]):
    """
    Dependency factory to require specific scopes
    """
    async def check_scopes(
        current_user: Dict[str, Any] = Depends(get_current_user)
    ) -> Dict[str, Any]:
        token_scopes = current_user.get("scope", "").split()
        
        # Check if token has all required scopes
        if not all(scope in token_scopes for scope in required_scopes):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Insufficient scopes. Required scopes: {required_scopes}"
            )
        
        return current_user
    
    return check_scopes
