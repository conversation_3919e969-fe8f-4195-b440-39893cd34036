from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from app.auth.keycloak_client import keycloak_client
from typing import Optional, Dict, Any
import logging

logger = logging.getLogger(__name__)

# Security scheme
security = HTTPBearer()


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> Dict[str, Any]:
    """
    Dependency to get current authenticated user from JW<PERSON> token
    """
    token = credentials.credentials
    
    # Verify token with Keycloak
    token_info = keycloak_client.verify_token(token)
    if not token_info:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # Additional check: introspect token to ensure it's active
    introspection = keycloak_client.introspect_token(token)
    if not introspection or not introspection.get("active", False):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token is not active",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return token_info


async def get_current_user_info(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> Dict[str, Any]:
    """
    Dependency to get current user info from Keycloak userinfo endpoint
    """
    token = credentials.credentials
    
    # Get user info from Keycloak
    user_info = keycloak_client.get_user_info(token)
    if not user_info:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not retrieve user information",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return user_info


def require_roles(required_roles: list[str]):
    """
    Dependency factory to require specific roles
    """
    async def check_roles(
        current_user: Dict[str, Any] = Depends(get_current_user)
    ) -> Dict[str, Any]:
        user_roles = current_user.get("realm_access", {}).get("roles", [])
        
        # Check if user has any of the required roles
        if not any(role in user_roles for role in required_roles):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Insufficient permissions. Required roles: {required_roles}"
            )
        
        return current_user
    
    return check_roles


def require_scope(required_scopes: list[str]):
    """
    Dependency factory to require specific scopes
    """
    async def check_scopes(
        current_user: Dict[str, Any] = Depends(get_current_user)
    ) -> Dict[str, Any]:
        token_scopes = current_user.get("scope", "").split()
        
        # Check if token has all required scopes
        if not all(scope in token_scopes for scope in required_scopes):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Insufficient scopes. Required scopes: {required_scopes}"
            )
        
        return current_user
    
    return check_scopes
