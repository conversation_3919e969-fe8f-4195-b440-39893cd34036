import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock
from app.main import app

client = TestClient(app)


def test_root_endpoint():
    """Test the root endpoint"""
    response = client.get("/")
    assert response.status_code == 200
    data = response.json()
    assert "message" in data
    assert "version" in data
    assert data["message"] == "FastAPI Keycloak PostgreSQL Integration"


@patch('app.database.connection.check_db_connection')
def test_health_check_healthy(mock_db_check):
    """Test health check when database is healthy"""
    mock_db_check.return_value = True
    
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"
    assert data["database"] == "connected"


@patch('app.database.connection.check_db_connection')
def test_health_check_unhealthy(mock_db_check):
    """Test health check when database is unhealthy"""
    mock_db_check.return_value = False
    
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "unhealthy"
    assert data["database"] == "disconnected"


def test_docs_endpoint():
    """Test that API documentation is accessible"""
    response = client.get("/docs")
    assert response.status_code == 200


def test_redoc_endpoint():
    """Test that ReDoc documentation is accessible"""
    response = client.get("/redoc")
    assert response.status_code == 200


def test_protected_endpoint_without_token():
    """Test that protected endpoints require authentication"""
    response = client.get("/auth/me")
    assert response.status_code == 401


def test_protected_endpoint_with_invalid_token():
    """Test that protected endpoints reject invalid tokens"""
    headers = {"Authorization": "Bearer invalid-token"}
    response = client.get("/auth/me", headers=headers)
    assert response.status_code == 401
