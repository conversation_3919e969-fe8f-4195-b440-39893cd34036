from pydantic_settings import BaseSettings
from pydantic import Field
from typing import Optional


class Settings(BaseSettings):
    # Database Configuration
    database_url: str = Field(default="sqlite:///./app.db")

    # Keycloak Configuration
    keycloak_server_url: str = Field(default="http://localhost:8080/auth/")
    keycloak_realm: str = Field(default="master")
    keycloak_client_id: str = Field(default="admin-cli")
    keycloak_client_secret: str = Field(default="")

    # JWT Configuration
    secret_key: str = Field(default="dev-secret-key-change-in-production")
    algorithm: str = Field(default="RS256")
    access_token_expire_minutes: int = Field(default=30)

    # Application Configuration
    environment: str = Field(default="development")
    debug: bool = Field(default=True)

    # Development mode - bypasses Keycloak for testing
    dev_mode: bool = Field(default=False)
    
    class Config:
        env_file = ".env"
        case_sensitive = False


# Global settings instance
settings = Settings()
