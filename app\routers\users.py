from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from app.auth.dependencies import get_current_user, require_roles
from app.database.connection import get_db
from app.crud import user as user_crud
from app.schemas.user import UserResponse, UserUpdate, UserProfileCreate, UserProfileUpdate, UserProfileResponse
from typing import Dict, Any, List

router = APIRouter(prefix="/users", tags=["users"])


@router.get("/", response_model=List[UserResponse])
async def get_users(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: Session = Depends(get_db),
    current_user: Dict[str, Any] = Depends(require_roles(["admin"]))
):
    """
    Get list of users (admin only)
    """
    return user_crud.get_users(db, skip=skip, limit=limit)


@router.get("/me", response_model=UserResponse)
async def get_current_user_profile(
    db: Session = Depends(get_db),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Get current user's profile from database
    """
    keycloak_id = current_user.get("sub")
    user = user_crud.get_user_by_keycloak_id(db, keycloak_id)
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found in database. Please sync your user first."
        )
    
    return user


@router.put("/me", response_model=UserResponse)
async def update_current_user(
    user_update: UserUpdate,
    db: Session = Depends(get_db),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Update current user's profile
    """
    keycloak_id = current_user.get("sub")
    user = user_crud.get_user_by_keycloak_id(db, keycloak_id)
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found in database. Please sync your user first."
        )
    
    updated_user = user_crud.update_user(db, user.id, user_update)
    if not updated_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Failed to update user"
        )
    
    return updated_user


@router.get("/{user_id}", response_model=UserResponse)
async def get_user_by_id(
    user_id: int,
    db: Session = Depends(get_db),
    current_user: Dict[str, Any] = Depends(require_roles(["admin"]))
):
    """
    Get user by ID (admin only)
    """
    user = user_crud.get_user_by_id(db, user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    return user


# User Profile endpoints
@router.get("/me/profile", response_model=UserProfileResponse)
async def get_current_user_profile_details(
    db: Session = Depends(get_db),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Get current user's detailed profile
    """
    keycloak_id = current_user.get("sub")
    user = user_crud.get_user_by_keycloak_id(db, keycloak_id)
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found in database"
        )
    
    profile = user_crud.get_user_profile(db, user.id)
    if not profile:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User profile not found"
        )
    
    return profile


@router.post("/me/profile", response_model=UserProfileResponse)
async def create_current_user_profile(
    profile_data: UserProfileCreate,
    db: Session = Depends(get_db),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Create current user's detailed profile
    """
    keycloak_id = current_user.get("sub")
    user = user_crud.get_user_by_keycloak_id(db, keycloak_id)
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found in database"
        )
    
    # Override user_id with current user's ID
    profile_data.user_id = user.id
    
    # Check if profile already exists
    existing_profile = user_crud.get_user_profile(db, user.id)
    if existing_profile:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User profile already exists"
        )
    
    return user_crud.create_user_profile(db, profile_data)


@router.put("/me/profile", response_model=UserProfileResponse)
async def update_current_user_profile(
    profile_update: UserProfileUpdate,
    db: Session = Depends(get_db),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Update current user's detailed profile
    """
    keycloak_id = current_user.get("sub")
    user = user_crud.get_user_by_keycloak_id(db, keycloak_id)
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found in database"
        )
    
    updated_profile = user_crud.update_user_profile(db, user.id, profile_update)
    if not updated_profile:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User profile not found"
        )
    
    return updated_profile
