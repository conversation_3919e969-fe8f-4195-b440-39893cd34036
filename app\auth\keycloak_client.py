from keycloak import KeycloakOpenID, KeycloakAdmin
from keycloak.exceptions import KeycloakError
from app.config import settings
import logging

logger = logging.getLogger(__name__)


class KeycloakClient:
    def __init__(self):
        self._keycloak_openid = None
        self._keycloak_admin = None

    @property
    def keycloak_openid(self):
        """Lazy initialization of KeycloakOpenID"""
        if self._keycloak_openid is None:
            self._keycloak_openid = KeycloakOpenID(
                server_url=settings.keycloak_server_url,
                client_id=settings.keycloak_client_id,
                realm_name=settings.keycloak_realm,
                client_secret_key=settings.keycloak_client_secret
            )
        return self._keycloak_openid

    @property
    def keycloak_admin(self):
        """Lazy initialization of KeycloakAdmin"""
        if self._keycloak_admin is None:
            try:
                self._keycloak_admin = KeycloakAdmin(
                    server_url=settings.keycloak_server_url,
                    client_id=settings.keycloak_client_id,
                    realm_name=settings.keycloak_realm,
                    client_secret_key=settings.keycloak_client_secret
                )
            except KeycloakError as e:
                logger.warning(f"KeycloakAdmin initialization failed: {e}")
                # Return None if admin access is not available
                return None
        return self._keycloak_admin
    
    def get_public_key(self):
        """Get Keycloak public key for token validation"""
        try:
            return self.keycloak_openid.public_key()
        except Exception as e:
            logger.error(f"Failed to get public key: {e}")
            raise
    
    def verify_token(self, token: str):
        """Verify JWT token with Keycloak"""
        try:
            # Decode and verify token
            token_info = self.keycloak_openid.decode_token(
                token,
                key=self.get_public_key(),
                options={"verify_signature": True, "verify_aud": False, "verify_exp": True}
            )
            return token_info
        except Exception as e:
            logger.error(f"Token verification failed: {e}")
            return None
    
    def get_user_info(self, token: str):
        """Get user information from token"""
        try:
            return self.keycloak_openid.userinfo(token)
        except Exception as e:
            logger.error(f"Failed to get user info: {e}")
            return None
    
    def introspect_token(self, token: str):
        """Introspect token to check if it's active"""
        try:
            return self.keycloak_openid.introspect(token)
        except Exception as e:
            logger.error(f"Token introspection failed: {e}")
            return None


# Global Keycloak client instance
keycloak_client = KeycloakClient()
