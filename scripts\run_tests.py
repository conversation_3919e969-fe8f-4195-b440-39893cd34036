#!/usr/bin/env python3
"""
Test runner script for FastAPI Keycloak PostgreSQL application
"""

import subprocess
import sys
import os

def run_command(command, description):
    """Run a shell command and handle errors"""
    print(f"Running: {description}")
    try:
        result = subprocess.run(command, shell=True, check=True)
        print(f"✓ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ {description} failed with exit code {e.returncode}")
        return False

def main():
    """Main test runner function"""
    print("FastAPI Keycloak PostgreSQL Test Runner")
    print("=" * 40)
    
    # Set environment variables for testing
    os.environ["DATABASE_URL"] = "sqlite:///./test.db"
    os.environ["KEYCLOAK_SERVER_URL"] = "http://localhost:8080/auth/"
    os.environ["KEYCLOAK_REALM"] = "test"
    os.environ["KEYCLOAK_CLIENT_ID"] = "test-client"
    os.environ["KEYCLOAK_CLIENT_SECRET"] = "test-secret"
    os.environ["SECRET_KEY"] = "test-secret-key"
    
    success = True
    
    # Run pytest
    if not run_command("python -m pytest tests/ -v", "Running unit tests"):
        success = False
    
    # Run type checking with mypy (if available)
    if not run_command("python -m mypy app/ --ignore-missing-imports", "Running type checks"):
        print("⚠️  Type checking failed (mypy may not be installed)")
    
    # Run linting with flake8 (if available)
    if not run_command("python -m flake8 app/ --max-line-length=100", "Running code linting"):
        print("⚠️  Linting failed (flake8 may not be installed)")
    
    print("\n" + "=" * 40)
    if success:
        print("✓ All tests passed!")
        sys.exit(0)
    else:
        print("✗ Some tests failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
