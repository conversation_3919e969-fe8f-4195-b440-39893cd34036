from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from app.auth.dependencies import get_current_user, get_current_user_info
from app.database.connection import get_db
from app.crud import user as user_crud
from app.schemas.user import User<PERSON><PERSON>, UserResponse
from typing import Dict, Any

router = APIRouter(prefix="/auth", tags=["authentication"])


@router.get("/me", response_model=Dict[str, Any])
async def get_current_user_endpoint(
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Get current authenticated user information from token
    """
    return {
        "user_id": current_user.get("sub"),
        "username": current_user.get("preferred_username"),
        "email": current_user.get("email"),
        "first_name": current_user.get("given_name"),
        "last_name": current_user.get("family_name"),
        "roles": current_user.get("realm_access", {}).get("roles", []),
        "scopes": current_user.get("scope", "").split()
    }


@router.get("/userinfo", response_model=Dict[str, Any])
async def get_user_info_endpoint(
    user_info: Dict[str, Any] = Depends(get_current_user_info)
):
    """
    Get current user information from Keycloak userinfo endpoint
    """
    return user_info


@router.post("/sync-user", response_model=UserResponse)
async def sync_user_with_database(
    db: Session = Depends(get_db),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Sync current user from Keycloak to local database
    """
    keycloak_id = current_user.get("sub")
    username = current_user.get("preferred_username")
    email = current_user.get("email")
    first_name = current_user.get("given_name")
    last_name = current_user.get("family_name")
    
    if not keycloak_id or not username or not email:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Missing required user information in token"
        )
    
    # Check if user already exists
    existing_user = user_crud.get_user_by_keycloak_id(db, keycloak_id)
    if existing_user:
        return existing_user
    
    # Create new user
    user_create = UserCreate(
        keycloak_id=keycloak_id,
        username=username,
        email=email,
        first_name=first_name,
        last_name=last_name
    )
    
    return user_crud.create_user(db, user_create)


@router.get("/validate-token")
async def validate_token(
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Validate if the current token is valid
    """
    return {
        "valid": True,
        "user_id": current_user.get("sub"),
        "username": current_user.get("preferred_username")
    }
