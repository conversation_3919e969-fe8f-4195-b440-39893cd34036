version: '3.8'

services:
  fastapi-app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - KEYCLOAK_SERVER_URL=${KEYCLOAK_SERVER_URL}
      - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_REALM=${<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_REALM}
      - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_CLIENT_ID=${KEY<PERSON>OAK_CLIENT_ID}
      - K<PERSON><PERSON><PERSON>OAK_CLIENT_SECRET=${KEYCLOAK_CLIENT_SECRET}
      - SECRET_KEY=${SECRET_KEY}
    volumes:
      - .:/app
    depends_on:
      - db
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

  # Local PostgreSQL for development (optional - you can use cloud server instead)
  db:
    image: postgres:15
    environment:
      POSTGRES_DB: fastapi_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
